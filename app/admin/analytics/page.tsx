import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Bar<PERSON>hart3, TrendingUp, Users, FileText, Calendar, Download, Refresh<PERSON>w, Eye } from "lucide-react"
import { prisma } from "@/lib/prisma"
import { requireAdmin } from "@/lib/auth-utils"
import { UserRole, UserStatus, PostStatus } from "@prisma/client"

async function getAnalyticsData() {
  const [
    userStats,
    contentStats,
    facultyStats,
    recentActivity,
    monthlyUserGrowth,
    popularPosts
  ] = await Promise.all([
    // User statistics
    Promise.all([
      prisma.user.count(),
      prisma.user.count({ where: { status: UserStatus.ACTIVE } }),
      prisma.user.count({ where: { role: UserRole.FACULTY } }),
      prisma.user.count({ where: { role: UserRole.STUDENT } }),
      prisma.user.count({ 
        where: { 
          createdAt: { 
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) 
          } 
        } 
      })
    ]),
    
    // Content statistics
    Promise.all([
      prisma.post.count(),
      prisma.post.count({ where: { status: PostStatus.PUBLISHED } }),
      prisma.post.aggregate({ _sum: { viewCount: true } }),
      prisma.category.count()
    ]),
    
    // Faculty statistics
    Promise.all([
      prisma.facultyProfile.count(),
      prisma.facultyPublication.count(),
      prisma.researchProject.count(),
      prisma.officeHour.count()
    ]),
    
    // Recent activity (last 7 days)
    Promise.all([
      prisma.user.count({ 
        where: { 
          createdAt: { 
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) 
          } 
        } 
      }),
      prisma.post.count({ 
        where: { 
          createdAt: { 
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) 
          } 
        } 
      }),
      prisma.facultyProfile.count({ 
        where: { 
          createdAt: { 
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) 
          } 
        } 
      })
    ]),
    
    // Monthly user growth (last 6 months)
    Promise.all(
      Array.from({ length: 6 }, (_, i) => {
        const date = new Date()
        date.setMonth(date.getMonth() - i)
        const startOfMonth = new Date(date.getFullYear(), date.getMonth(), 1)
        const endOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0)
        
        return prisma.user.count({
          where: {
            createdAt: {
              gte: startOfMonth,
              lte: endOfMonth
            }
          }
        }).then(count => ({
          month: date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
          count
        }))
      })
    ),
    
    // Popular posts
    prisma.post.findMany({
      select: {
        title: true,
        viewCount: true,
        status: true,
        createdAt: true,
        author: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        viewCount: 'desc'
      },
      take: 5
    })
  ])

  const [totalUsers, activeUsers, facultyCount, studentCount, newUsersThisMonth] = userStats
  const [totalPosts, publishedPosts, totalViews, totalCategories] = contentStats
  const [totalFaculty, totalPublications, totalProjects, totalOfficeHours] = facultyStats
  const [newUsersThisWeek, newPostsThisWeek, newFacultyThisWeek] = recentActivity

  return {
    userStats: {
      total: totalUsers,
      active: activeUsers,
      faculty: facultyCount,
      students: studentCount,
      newThisMonth: newUsersThisMonth,
      newThisWeek: newUsersThisWeek
    },
    contentStats: {
      totalPosts,
      publishedPosts,
      totalViews: totalViews._sum.viewCount || 0,
      totalCategories,
      newThisWeek: newPostsThisWeek
    },
    facultyStats: {
      total: totalFaculty,
      publications: totalPublications,
      projects: totalProjects,
      officeHours: totalOfficeHours,
      newThisWeek: newFacultyThisWeek
    },
    monthlyGrowth: monthlyUserGrowth.reverse(),
    popularPosts
  }
}

export default async function AnalyticsPage() {
  // Require admin access
  await requireAdmin()
  
  const data = await getAnalyticsData()

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
          <p className="text-gray-600">System insights and performance metrics</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.userStats.total}</div>
            <p className="text-xs text-muted-foreground">
              +{data.userStats.newThisMonth} this month
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.userStats.active}</div>
            <p className="text-xs text-muted-foreground">
              {Math.round((data.userStats.active / data.userStats.total) * 100)}% of total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Content Views</CardTitle>
            <Eye className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.contentStats.totalViews.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Across {data.contentStats.publishedPosts} published posts
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Faculty</CardTitle>
            <Users className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.facultyStats.total}</div>
            <p className="text-xs text-muted-foreground">
              {data.facultyStats.publications} publications
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity (Last 7 Days)</CardTitle>
          <CardDescription>System activity overview</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">{data.userStats.newThisWeek}</div>
              <p className="text-sm text-gray-500">New Users</p>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">{data.contentStats.newThisWeek}</div>
              <p className="text-sm text-gray-500">New Posts</p>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600">{data.facultyStats.newThisWeek}</div>
              <p className="text-sm text-gray-500">New Faculty</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* User Growth Chart */}
      <Card>
        <CardHeader>
          <CardTitle>User Growth (Last 6 Months)</CardTitle>
          <CardDescription>Monthly user registration trends</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.monthlyGrowth.map((month, index) => (
              <div key={index} className="flex items-center justify-between">
                <span className="text-sm font-medium">{month.month}</span>
                <div className="flex items-center space-x-2">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ 
                        width: `${Math.max((month.count / Math.max(...data.monthlyGrowth.map(m => m.count))) * 100, 5)}%` 
                      }}
                    />
                  </div>
                  <span className="text-sm font-bold w-8">{month.count}</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Popular Content */}
      <Card>
        <CardHeader>
          <CardTitle>Popular Content</CardTitle>
          <CardDescription>Most viewed posts</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.popularPosts.map((post, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <h4 className="font-medium">{post.title}</h4>
                  <p className="text-sm text-gray-500">
                    By {post.author.name} • {new Date(post.createdAt).toLocaleDateString()}
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant={post.status === 'PUBLISHED' ? 'default' : 'secondary'}>
                    {post.status}
                  </Badge>
                  <span className="text-sm font-bold">{post.viewCount} views</span>
                </div>
              </div>
            ))}
            
            {data.popularPosts.length === 0 && (
              <div className="text-center py-4 text-gray-500">
                <p>No posts found</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* System Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Content Statistics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span>Total Posts</span>
                <span className="font-bold">{data.contentStats.totalPosts}</span>
              </div>
              <div className="flex justify-between">
                <span>Published Posts</span>
                <span className="font-bold">{data.contentStats.publishedPosts}</span>
              </div>
              <div className="flex justify-between">
                <span>Categories</span>
                <span className="font-bold">{data.contentStats.totalCategories}</span>
              </div>
              <div className="flex justify-between">
                <span>Total Views</span>
                <span className="font-bold">{data.contentStats.totalViews.toLocaleString()}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Faculty Statistics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span>Total Faculty</span>
                <span className="font-bold">{data.facultyStats.total}</span>
              </div>
              <div className="flex justify-between">
                <span>Publications</span>
                <span className="font-bold">{data.facultyStats.publications}</span>
              </div>
              <div className="flex justify-between">
                <span>Research Projects</span>
                <span className="font-bold">{data.facultyStats.projects}</span>
              </div>
              <div className="flex justify-between">
                <span>Office Hours</span>
                <span className="font-bold">{data.facultyStats.officeHours}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
