import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Users, FileText, Building2, BookOpen, TrendingUp, Calendar } from "lucide-react"
import { prisma } from "@/lib/prisma"
import { UserRole, UserStatus } from "@prisma/client"

async function getDashboardStats() {
  const [
    totalUsers,
    activeFaculty,
    totalPosts,
    publishedPosts,
    totalDepartments,
    totalPrograms
  ] = await Promise.all([
    prisma.user.count(),
    prisma.user.count({
      where: {
        role: UserRole.FACULTY,
        status: UserStatus.ACTIVE
      }
    }),
    prisma.post.count(),
    prisma.post.count({
      where: {
        status: 'PUBLISHED'
      }
    }),
    prisma.department.count(),
    prisma.program.count()
  ])

  return {
    totalUsers,
    activeFaculty,
    totalPosts,
    publishedPosts,
    totalDepartments,
    totalPrograms
  }
}

async function getRecentActivity() {
  const recentPosts = await prisma.post.findMany({
    take: 5,
    orderBy: {
      createdAt: 'desc'
    },
    include: {
      author: {
        select: {
          name: true,
          email: true
        }
      },
      category: {
        select: {
          name: true
        }
      }
    }
  })

  const recentUsers = await prisma.user.findMany({
    take: 5,
    orderBy: {
      createdAt: 'desc'
    },
    select: {
      id: true,
      name: true,
      email: true,
      role: true,
      status: true,
      createdAt: true
    }
  })

  return {
    recentPosts,
    recentUsers
  }
}

export default async function AdminDashboard() {
  const stats = await getDashboardStats()
  const activity = await getRecentActivity()

  const statCards = [
    {
      title: "Total Users",
      value: stats.totalUsers,
      description: "All registered users",
      icon: Users,
      color: "text-blue-600"
    },
    {
      title: "Active Faculty",
      value: stats.activeFaculty,
      description: "Faculty members",
      icon: Users,
      color: "text-green-600"
    },
    {
      title: "Published Posts",
      value: `${stats.publishedPosts}/${stats.totalPosts}`,
      description: "Content articles",
      icon: FileText,
      color: "text-purple-600"
    },
    {
      title: "Departments",
      value: stats.totalDepartments,
      description: "Academic departments",
      icon: Building2,
      color: "text-orange-600"
    },
    {
      title: "Programs",
      value: stats.totalPrograms,
      description: "Academic programs",
      icon: BookOpen,
      color: "text-indigo-600"
    },
    {
      title: "Growth",
      value: "+12%",
      description: "This month",
      icon: TrendingUp,
      color: "text-emerald-600"
    }
  ]

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Welcome to the administration panel</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {statCards.map((stat, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <stat.icon className={`h-4 w-4 ${stat.color}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">
                {stat.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Posts */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Posts</CardTitle>
            <CardDescription>
              Latest content updates
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {activity.recentPosts.length > 0 ? (
                activity.recentPosts.map((post) => (
                  <div key={post.id} className="flex items-center space-x-4">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {post.title}
                      </p>
                      <p className="text-sm text-gray-500">
                        by {post.author.name || post.author.email} • {post.category.name}
                      </p>
                    </div>
                    <div className="text-xs text-gray-400">
                      {new Date(post.createdAt).toLocaleDateString()}
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-sm text-gray-500">No posts yet</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Recent Users */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Users</CardTitle>
            <CardDescription>
              Newly registered users
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {activity.recentUsers.length > 0 ? (
                activity.recentUsers.map((user) => (
                  <div key={user.id} className="flex items-center space-x-4">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {user.name || user.email}
                      </p>
                      <p className="text-sm text-gray-500 capitalize">
                        {user.role.toLowerCase().replace('_', ' ')} • {user.status.toLowerCase()}
                      </p>
                    </div>
                    <div className="text-xs text-gray-400">
                      {new Date(user.createdAt).toLocaleDateString()}
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-sm text-gray-500">No users yet</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common administrative tasks
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a
              href="/admin/faculty/new"
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Users className="h-8 w-8 text-blue-600 mr-3" />
              <div>
                <h3 className="font-medium">Add Faculty</h3>
                <p className="text-sm text-gray-500">Create new faculty profile</p>
              </div>
            </a>
            <a
              href="/admin/posts/new"
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <FileText className="h-8 w-8 text-green-600 mr-3" />
              <div>
                <h3 className="font-medium">New Post</h3>
                <p className="text-sm text-gray-500">Create content article</p>
              </div>
            </a>
            <a
              href="/admin/departments/new"
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Building2 className="h-8 w-8 text-purple-600 mr-3" />
              <div>
                <h3 className="font-medium">Add Department</h3>
                <p className="text-sm text-gray-500">Create new department</p>
              </div>
            </a>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
