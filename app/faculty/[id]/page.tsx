import Link from 'next/link'
import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { PageTransition } from "@/components/ui/page-transition"
import { SkipLink } from "@/components/ui/skip-link"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  ArrowLeft,
  Mail,
  Globe,
  BookOpen,
  Award,
  FileText,
  Briefcase,
  Clock,
  FileDown,
  UserPlus,
  Menu,
  ChevronRight,
  BarChart,
  User as UserIcon,
  MapPin,
  Link2,
  Bookmark,
  Calendar,
  Users as UsersIcon,
  ExternalLink,
  Quote,
  Hash,
  GraduationCap,
  ArrowUpRight,
  TrendingUp,
  Star,
  Eye,
  Download,
  Share2,
  MessageCircle,
  Phone,
  Building,
  Target,
  Zap,
  Trophy,
  BookMarked,
  Lightbulb,
  Network
} from "lucide-react"
import { LazyImage } from "@/components/ui/lazy-image"

// Import Prisma types
import { 
  User, 
  UserProfile, 
  FacultyProfile, 
  Department, 
  FacultyEducation, 
  FacultyPublication, 
  FacultyResearchArea, 
  FacultyTimeline, 
  FacultyIndustryExperience, 
  FacultySkill, 
  ResearchProject, 
  CourseClass,
  Prisma
} from "@prisma/client"

// Import shared components
import { AcademicTimeline } from "@/components/ui/academic-timeline"
import { CVDownload } from "@/components/ui/cv-download"
import { ScholarlyPublications } from "@/components/ui/scholarly-publications"
import { UpcomingClasses } from "@/components/ui/upcoming-classes"
import { OfficeHoursScheduler } from "@/components/ui/office-hours-scheduler"
import { ResearchOpportunities } from "@/components/ui/research-opportunities"
import { ResearchAreas } from "@/components/ui/research-areas"
import { EducationTimeline } from "@/components/ui/education-timeline"

// Import client component
import { FacultyProfileClient } from "@/components/faculty/FacultyProfileClient"
import { Breadcrumb, BreadcrumbItem } from "@/components/ui/breadcrumb"
import { prisma } from "@/lib/prisma"

// Define a comprehensive type for the faculty data
type FacultyPageData = User & {
  profile: UserProfile | null;
  facultyProfile: FacultyProfile & {
    department: Department;
    education: FacultyEducation[];
    publications: FacultyPublication[];
    researchAreas: FacultyResearchArea[];
    timeline: FacultyTimeline[];
    industryExperience: FacultyIndustryExperience[];
    skills: FacultySkill[];
    researchProjects: ResearchProject[];
    classes: CourseClass[];
  } & {
    officeLocation?: string | null;
    websiteUrl?: string | null;
    scholarId?: string | null;
    bio?: string | null;
    courses?: string[]; 
    upcomingClasses?: CourseClass[]; 
    officeHours?: string | null;
    scheduledOfficeHours?: any[]; 
    citationCount?: number | null;
    hIndex?: number | null;
  };
};

interface FacultyPageProps {
  params: {
    id: string
  }
}

async function getFacultyById(id: string): Promise<FacultyPageData | null> {
  const user = await prisma.user.findUnique({
    where: {
      id,
      role: 'FACULTY',
      status: 'ACTIVE',
    },
    include: {
      profile: true,
      facultyProfile: {
        include: {
          department: true,
          education: {
            orderBy: {
              year: 'desc'
            }
          },
          publications: {
            orderBy: {
              year: 'desc'
            }
          },
          researchAreas: true,
          timeline: {
            orderBy: {
              year: 'desc'
            }
          },
          industryExperience: {
            orderBy: {
              startDate: 'desc'
            }
          },
          skills: {
            orderBy: [
              {
                category: 'asc'
              },
              {
                skillName: 'asc'
              }
            ]
          },
          researchProjects: true, 
          classes: {
            include: {
              course: true
            }
          }
        }
      }
    }
  })

  if (!user || !user.facultyProfile) {
    return null
  }

  return user as FacultyPageData;
}

export default async function FacultyProfilePage({ params }: FacultyPageProps) {
  const { id } = await params;
  const faculty: FacultyPageData | null = await getFacultyById(id);

  if (!faculty || !faculty.facultyProfile) {
    return (
      <div className="flex min-h-screen flex-col">
        <Header />
        <main className="flex-1 flex items-center justify-center">
          <div className="text-center max-w-md p-8">
            <h1 className="text-xl font-medium mb-2 text-gray-900">Faculty Not Found</h1>
            <p className="mb-6 text-muted-foreground text-sm">
              We couldn't find the faculty member you're looking for. They may have moved to a different department or the URL might be incorrect.
            </p>
            <Link href="/faculty">
              <Button variant="outline" size="sm" className="h-9 px-4 border-crimson/20 hover:bg-crimson/5">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Faculty Directory
              </Button>
            </Link>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  // Calculate years of experience
  const currentYear = new Date().getFullYear()
  const earliestYear = faculty.facultyProfile.timeline.length > 0
    ? Math.min(...faculty.facultyProfile.timeline.map(t => parseInt(t.year.split('-')[0])))
    : currentYear
  const yearsExperience = currentYear - earliestYear

  // Build breadcrumb items
  const breadcrumbItems: BreadcrumbItem[] = [
    { label: 'Faculty', href: '/faculty' },
    { label: faculty.facultyProfile.department.name, href: `/faculty?department=${encodeURIComponent(faculty.facultyProfile.department.name)}` },
    { label: faculty.name || '', href: `/faculty/${faculty.id}`, current: true }
  ];

  // Table of contents items (using string identifiers instead of icon components)
  const tocItems = [
    { id: 'hero', label: 'Overview', icon: 'User', count: undefined },
    { id: 'about', label: 'About', icon: 'User', count: faculty.facultyProfile.education.length + faculty.facultyProfile.skills.length },
    { id: 'publications', label: 'Publications', icon: 'BookOpen', count: faculty.facultyProfile.publications.length },
    { id: 'research', label: 'Research', icon: 'Lightbulb', count: faculty.facultyProfile.researchAreas.length + faculty.facultyProfile.researchProjects.length },
    { id: 'teaching', label: 'Teaching', icon: 'GraduationCap', count: faculty.facultyProfile.classes.length },
    { id: 'experience', label: 'Experience', icon: 'Briefcase', count: faculty.facultyProfile.industryExperience.length }
  ];

  return (
    <FacultyProfileClient
      faculty={faculty}
      breadcrumbItems={breadcrumbItems}
      tocItems={tocItems}
      yearsExperience={yearsExperience}
    />
  )
}

export async function generateStaticParams() {
  const users = await prisma.user.findMany({
    where: {
      role: 'FACULTY',
      status: 'ACTIVE',
    },
    select: {
      id: true,
    },
  });

  return users.map((user) => ({
    id: user.id,
  }));
}