import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    // Get all table names from the database
    const tableResult = await prisma.$queryRaw<Array<{ table_name: string }>>`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_type = 'BASE TABLE'
      ORDER BY table_name;
    `

    const tables = []

    for (const table of tableResult) {
      const tableName = table.table_name

      try {
        // Get column information
        const columnResult = await prisma.$queryRaw<Array<{
          column_name: string
          data_type: string
          is_nullable: string
          column_default: string | null
        }>>`
          SELECT 
            column_name,
            data_type,
            is_nullable,
            column_default
          FROM information_schema.columns 
          WHERE table_schema = 'public' 
          AND table_name = ${tableName}
          ORDER BY ordinal_position;
        `

        // Get record count
        const countResult = await prisma.$queryRawUnsafe(`SELECT COUNT(*) as count FROM "${tableName}"`)
        const count = Number((countResult as any)[0]?.count || 0)

        // Format columns
        const columns = columnResult.map(col => ({
          name: col.column_name,
          type: col.data_type,
          nullable: col.is_nullable === 'YES',
          default: col.column_default
        }))

        tables.push({
          name: tableName,
          count,
          columns
        })
      } catch (error) {
        console.error(`Error processing table ${tableName}:`, error)
        // Skip tables that cause errors but continue with others
        tables.push({
          name: tableName,
          count: 0,
          columns: []
        })
      }
    }

    return NextResponse.json({
      success: true,
      tables
    })

  } catch (error) {
    console.error('Database schema error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch database schema',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
} 