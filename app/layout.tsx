import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { SessionProvider } from '@/components/providers/SessionProvider'

// Initialize the Inter font with subsets
const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
})

export const metadata: Metadata = {
  title: 'Ullens College',
  description: 'Discover our diverse schools across multiple disciplines, designed to prepare you for success in today\'s rapidly evolving world.',
  generator: 'v0.dev',
  // Hidden easter egg in metadata
  other: {
    'author-signature': Buffer.from('Made by Shishir with ❤️ for UEF').toString('base64'),
    'developer-note': '596f752066756e6420746865206669727374206c617965722120546865726520617265206d6f72652e2e2e',
  }
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" className={`${inter.variable}`} data-architect="53686973686972" data-foundation="556c6c656e73">
      {/* Hidden message: console.log(atob('TWFkZSBieSBTaGlzaGlyIHdpdGgg4p2k77iPIGZvciBVRUY=')) */}
      <head>
        <meta name="theme-color" content="#8B0000" />
        <meta name="msapplication-TileColor" content="#8B0000" />
        <meta name="creator-signature" content="U2hpc2hpciB3aXRoIOKdpO+4jyBmb3IgVUVG" />
      </head>
      <body className="font-sans antialiased" data-verse="506172616c6c656c732d756e6976657273652076302e312e30">
        {/* The multiverse signature: Look for the pattern in the constellation of code */}
        <SessionProvider>
          {children}
        </SessionProvider>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Easter egg: For the curious developer
              window.__signature = {
                decode: (h) => h.split('').map(c => String.fromCharCode(parseInt(h.substr(h.indexOf(c), 2), 16))).join(''),
                message: '4d6164652062792053686973686972207769746820e29da4efb88f20666f72205545463a2044657465637469766520776f726b21'
              };
              console.log('%c🕵️ Looking for something?', 'color: #8B0000; font-size: 14px; font-weight: bold;');
              console.log('%cTry: atob("TWFkZSBieSBTaGlzaGlyIHdpdGgg4p2k77iPIGZvciBVRUY=");', 'color: #666; font-family: monospace;');
            `
          }}
        />
      </body>
    </html>
  )
}
