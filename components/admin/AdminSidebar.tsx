"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import {
  Users,
  FileText,
  Building2,
  BookOpen,
  Calendar,
  Settings,
  BarChart3,
  Home,
  UserCheck,
  GraduationCap,
  Database
} from "lucide-react"
import { UserRole } from "@prisma/client"

interface AdminSidebarProps {
  user: {
    id: string
    email: string
    name?: string | null
    role: UserRole
  }
}

const navigation = [
  {
    name: "Dashboard",
    href: "/admin",
    icon: Home
  },
  {
    name: "Faculty Management",
    href: "/admin/faculty",
    icon: GraduationCap
  },
  {
    name: "User Management",
    href: "/admin/users",
    icon: Users
  },
  {
    name: "Content Management",
    href: "/admin/posts",
    icon: FileText
  },
  {
    name: "Departments",
    href: "/admin/departments",
    icon: Building2
  },
  {
    name: "Programs",
    href: "/admin/programs",
    icon: BookOpen
  },
  {
    name: "Courses",
    href: "/admin/courses",
    icon: Calendar
  },
  {
    name: "Analytics",
    href: "/admin/analytics",
    icon: BarChart3
  },
  {
    name: "Database Debug",
    href: "/admin/database",
    icon: Database
  },
  {
    name: "Settings",
    href: "/admin/settings",
    icon: Settings
  }
]

export function AdminSidebar({ user }: AdminSidebarProps) {
  const pathname = usePathname()

  return (
    <div className="flex flex-col w-64 bg-white shadow-lg">
      {/* Logo/Brand */}
      <div className="flex items-center justify-center h-16 px-4 bg-blue-600 text-white">
        <h1 className="text-xl font-bold">Admin Panel</h1>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-2">
        {navigation.map((item) => {
          const isActive = pathname === item.href ||
            (item.href !== "/admin" && pathname.startsWith(item.href))

          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                "flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors",
                isActive
                  ? "bg-blue-100 text-blue-700"
                  : "text-gray-600 hover:bg-gray-100 hover:text-gray-900"
              )}
            >
              <item.icon className="w-5 h-5 mr-3" />
              {item.name}
            </Link>
          )
        })}
      </nav>

      {/* User info */}
      <div className="px-4 py-4 border-t border-gray-200">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
              <UserCheck className="w-4 h-4 text-white" />
            </div>
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-gray-700">
              {user.name || user.email}
            </p>
            <p className="text-xs text-gray-500 capitalize">
              {user.role.toLowerCase().replace('_', ' ')}
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
