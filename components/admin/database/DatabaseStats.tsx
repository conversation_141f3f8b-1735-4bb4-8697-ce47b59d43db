import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Database, Table, Users, FileText } from 'lucide-react'

interface DatabaseTable {
  name: string
  count: number
  columns: Array<{
    name: string
    type: string
    nullable: boolean
    default: string | null
  }>
}

interface DatabaseStatsProps {
  tables: DatabaseTable[]
}

export function DatabaseStats({ tables }: DatabaseStatsProps) {
  const totalRecords = tables.reduce((sum, table) => sum + table.count, 0)
  const totalColumns = tables.reduce((sum, table) => sum + table.columns.length, 0)
  
  // Categorize tables by common patterns
  const userTables = tables.filter(t => 
    t.name.includes('user') || t.name.includes('profile') || t.name.includes('auth')
  )
  const contentTables = tables.filter(t => 
    t.name.includes('post') || t.name.includes('page') || t.name.includes('content')
  )
  const academicTables = tables.filter(t => 
    t.name.includes('faculty') || t.name.includes('course') || t.name.includes('program') || 
    t.name.includes('department') || t.name.includes('student')
  )

  const stats = [
    {
      title: 'Total Tables',
      value: tables.length,
      icon: Database,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      title: 'Total Records',
      value: totalRecords.toLocaleString(),
      icon: FileText,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      title: 'Total Columns',
      value: totalColumns,
      icon: Table,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    },
    {
      title: 'User Tables',
      value: userTables.length,
      icon: Users,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Enhanced Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <Card key={index} className="border-0 bg-white/80 backdrop-blur-sm shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-600">
                    {stat.title}
                  </p>
                  <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                </div>
                <div className={`p-4 rounded-2xl ${stat.bgColor} shadow-lg`}>
                  <stat.icon className={`h-8 w-8 ${stat.color}`} />
                </div>
              </div>
              
              {/* Progress bar effect */}
              <div className="mt-4 h-2 bg-gray-100 rounded-full overflow-hidden">
                <div 
                  className={`h-full ${stat.color.replace('text-', 'bg-')} rounded-full transition-all duration-1000 ease-out`}
                  style={{ 
                    width: `${Math.min(100, (typeof stat.value === 'string' ? parseInt(stat.value.replace(/,/g, '')) : stat.value) / 10)}%`,
                    animationDelay: `${index * 200}ms`
                  }}
                ></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
      
      {/* Enhanced Table Categories */}
      <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-xl">
        <CardHeader className="bg-gradient-to-r from-slate-50 to-gray-50 rounded-t-lg">
          <CardTitle className="flex items-center text-gray-800">
            <div className="rounded-lg bg-indigo-100 p-2 mr-3">
              <Database className="h-5 w-5 text-indigo-600" />
            </div>
            Table Categories
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="space-y-6">
            {/* User & Auth Tables */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-semibold text-lg flex items-center text-blue-800">
                  <div className="rounded-lg bg-blue-100 p-2 mr-3">
                    <Users className="h-5 w-5 text-blue-600" />
                  </div>
                  User & Auth Tables
                </h4>
                <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 shrink-0">
                  {userTables.length} tables
                </Badge>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                {userTables.map(table => (
                  <div key={table.name} className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-100 min-w-0">
                    <span className="font-medium text-blue-900 text-sm truncate mr-2" title={table.name}>
                      {table.name}
                    </span>
                    <Badge variant="secondary" className="bg-blue-100 text-blue-700 shrink-0">
                      {table.count.toLocaleString()}
                    </Badge>
                  </div>
                ))}
                {userTables.length === 0 && (
                  <div className="col-span-full">
                    <p className="text-sm text-gray-500 italic text-center py-4">No user tables found</p>
                  </div>
                )}
              </div>
            </div>
            
            {/* Content Tables */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-semibold text-lg flex items-center text-green-800">
                  <div className="rounded-lg bg-green-100 p-2 mr-3">
                    <FileText className="h-5 w-5 text-green-600" />
                  </div>
                  Content Tables
                </h4>
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 shrink-0">
                  {contentTables.length} tables
                </Badge>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                {contentTables.map(table => (
                  <div key={table.name} className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-100 min-w-0">
                    <span className="font-medium text-green-900 text-sm truncate mr-2" title={table.name}>
                      {table.name}
                    </span>
                    <Badge variant="secondary" className="bg-green-100 text-green-700 shrink-0">
                      {table.count.toLocaleString()}
                    </Badge>
                  </div>
                ))}
                {contentTables.length === 0 && (
                  <div className="col-span-full">
                    <p className="text-sm text-gray-500 italic text-center py-4">No content tables found</p>
                  </div>
                )}
              </div>
            </div>
            
            {/* Academic Tables */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-semibold text-lg flex items-center text-purple-800">
                  <div className="rounded-lg bg-purple-100 p-2 mr-3">
                    <Database className="h-5 w-5 text-purple-600" />
                  </div>
                  Academic Tables
                </h4>
                <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200 shrink-0">
                  {academicTables.length} tables
                </Badge>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                {academicTables.map(table => (
                  <div key={table.name} className="flex items-center justify-between p-3 bg-purple-50 rounded-lg border border-purple-100 min-w-0">
                    <span className="font-medium text-purple-900 text-sm truncate mr-2" title={table.name}>
                      {table.name}
                    </span>
                    <Badge variant="secondary" className="bg-purple-100 text-purple-700 shrink-0">
                      {table.count.toLocaleString()}
                    </Badge>
                  </div>
                ))}
                {academicTables.length === 0 && (
                  <div className="col-span-full">
                    <p className="text-sm text-gray-500 italic text-center py-4">No academic tables found</p>
                  </div>
                )}
              </div>
            </div>

            {/* Other Tables */}
            {tables.filter(table => 
              !userTables.includes(table) && 
              !contentTables.includes(table) && 
              !academicTables.includes(table)
            ).length > 0 && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-semibold text-lg flex items-center text-gray-800">
                    <div className="rounded-lg bg-gray-100 p-2 mr-3">
                      <Database className="h-5 w-5 text-gray-600" />
                    </div>
                    Other Tables
                  </h4>
                  <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200 shrink-0">
                    {tables.filter(table => 
                      !userTables.includes(table) && 
                      !contentTables.includes(table) && 
                      !academicTables.includes(table)
                    ).length} tables
                  </Badge>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                  {tables
                    .filter(table => 
                      !userTables.includes(table) && 
                      !contentTables.includes(table) && 
                      !academicTables.includes(table)
                    )
                    .map(table => (
                      <div key={table.name} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-100 min-w-0">
                        <span className="font-medium text-gray-900 text-sm truncate mr-2" title={table.name}>
                          {table.name}
                        </span>
                        <Badge variant="secondary" className="bg-gray-100 text-gray-700 shrink-0">
                          {table.count.toLocaleString()}
                        </Badge>
                      </div>
                    ))}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 