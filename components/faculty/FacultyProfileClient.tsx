'use client'

import React from 'react'
import Link from 'next/link'
import { PageTransition } from "@/components/ui/page-transition"
import { SkipLink } from "@/components/ui/skip-link"
import { Button } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import {
  ArrowLeft,
  BookOpen,
  Award,
  FileText,
  Briefcase,
  Clock,
  Share2,
  User,
  GraduationCap,
  Lightbulb,
  Calendar,
  Building
} from "lucide-react"

// Import enhanced components
import { FacultyStatsCards, QuickActionButtons, AchievementBadges } from "@/components/ui/faculty-stats-cards"
import { FloatingToc, useActiveSection } from "@/components/ui/floating-toc"
import { FacultyHeroSection } from "@/components/ui/faculty-hero-section"
import { Breadcrumb, BreadcrumbItem } from "@/components/ui/breadcrumb"
import { PrintButton } from "@/components/ui/print-button"
import { PrintStyles } from "@/components/ui/print-styles"

// Import existing components
import { AcademicTimeline } from "@/components/ui/academic-timeline"
import { ScholarlyPublications } from "@/components/ui/scholarly-publications"
import { UpcomingClasses } from "@/components/ui/upcoming-classes"
import { ResearchOpportunities } from "@/components/ui/research-opportunities"
import { ResearchAreas } from "@/components/ui/research-areas"
import { EducationTimeline } from "@/components/ui/education-timeline"
import { PublicationChart, ResearchImpact } from "@/components/ui/publication-chart"
import { SkillsRadar, SkillCategoryBreakdown } from "@/components/ui/skills-radar"
import { ContactCard } from "@/components/ui/contact-card"
import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"

// Types
interface FacultyProfileClientProps {
  faculty: any // Replace with proper type
  breadcrumbItems: BreadcrumbItem[]
  tocItems: any[]
  yearsExperience: number
}

export function FacultyProfileClient({ 
  faculty, 
  breadcrumbItems, 
  tocItems, 
  yearsExperience 
}: FacultyProfileClientProps) {
  const activeSection = useActiveSection(tocItems.map(item => item.id))

  return (
    <PageTransition transitionType="fade" duration={300}>
      <div className="flex min-h-screen flex-col">
        <SkipLink />
        <Header />
        <PrintStyles selector="#print-content" />
        
        {/* Floating Table of Contents */}
        <FloatingToc items={tocItems} activeSection={activeSection} />
        
        <main id="main-content" className="flex-1 bg-light/20" tabIndex={-1}>
          {/* Breadcrumb Navigation */}
          <div className="bg-white border-b border-gray-100">
            <div className="container mx-auto px-4 max-w-6xl py-3">
              <Breadcrumb items={breadcrumbItems} />
            </div>
          </div>

          {/* Back Button and Actions */}
          <div className="bg-white border-b border-gray-50">
            <div className="container mx-auto px-4 max-w-6xl py-4">
              <div className="flex justify-between items-center">
                <Link href="/faculty" aria-label="Back to Faculty Directory">
                  <Button variant="ghost" size="sm" className="text-gray-600 hover:text-gray-900">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Faculty Directory
                  </Button>
                </Link>

                <div className="flex items-center gap-3">
                  <Button variant="outline" size="sm" className="hidden sm:flex">
                    <Share2 className="h-4 w-4 mr-2" />
                    Share Profile
                  </Button>
                  <PrintButton variant="outline" size="sm" />
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Hero Section */}
          <section id="hero" className="bg-white">
            <FacultyHeroSection
              name={faculty.name || "Faculty Member"}
              title={faculty.facultyProfile.title}
              bio={faculty.facultyProfile.bio}
              department={faculty.facultyProfile.department}
              officeLocation={faculty.facultyProfile.officeLocation}
              officeHours={faculty.facultyProfile.officeHours}
              imageUrl={faculty.profile?.avatarUrl || faculty.image || "/images/faculty/default-avatar.svg"}
              researchAreas={faculty.facultyProfile.researchAreas}
            />
          </section>

          {/* Stats and Quick Actions */}
          <section className="bg-white border-b border-gray-100">
            <div className="container mx-auto px-4 max-w-6xl py-8">
              {/* Achievement Badges */}
              <AchievementBadges
                publications={faculty.facultyProfile.publications.length}
                citationCount={faculty.facultyProfile.citationCount}
                yearsExperience={yearsExperience}
              />

              {/* Quick Action Buttons */}
              <QuickActionButtons
                email={faculty.email}
                websiteUrl={faculty.facultyProfile.websiteUrl}
                scholarId={faculty.facultyProfile.scholarId}
                phone={faculty.profile?.phone}
              />

              {/* Statistics Cards */}
              <FacultyStatsCards
                publications={faculty.facultyProfile.publications.length}
                citationCount={faculty.facultyProfile.citationCount}
                hIndex={faculty.facultyProfile.hIndex}
                yearsExperience={yearsExperience}
                researchAreas={faculty.facultyProfile.researchAreas.length}
                currentClasses={faculty.facultyProfile.classes.length}
              />
            </div>
          </section>

          {/* Main Content */}
          <section className="bg-light/20">
            <div id="print-content" className="container mx-auto px-4 max-w-6xl py-12">
              <div className="max-w-5xl mx-auto">

                {/* Main content tabs */}
                <Tabs defaultValue="about" className="w-full">
                  <div className="sticky top-0 bg-white/95 backdrop-blur-sm z-30 border-b border-gray-100 mb-8">
                    <TabsList className="grid w-full grid-cols-5 h-12">
                      <TabsTrigger
                        value="about"
                        className="data-[state=active]:bg-crimson/10 data-[state=active]:text-crimson font-medium"
                      >
                        <User className="h-4 w-4 mr-2" />
                        About
                      </TabsTrigger>
                      <TabsTrigger
                        value="publications"
                        className="data-[state=active]:bg-crimson/10 data-[state=active]:text-crimson font-medium"
                      >
                        <BookOpen className="h-4 w-4 mr-2" />
                        Publications
                      </TabsTrigger>
                      <TabsTrigger
                        value="research"
                        className="data-[state=active]:bg-crimson/10 data-[state=active]:text-crimson font-medium"
                      >
                        <Lightbulb className="h-4 w-4 mr-2" />
                        Research
                      </TabsTrigger>
                      <TabsTrigger
                        value="teaching"
                        className="data-[state=active]:bg-crimson/10 data-[state=active]:text-crimson font-medium"
                      >
                        <GraduationCap className="h-4 w-4 mr-2" />
                        Teaching
                      </TabsTrigger>
                      <TabsTrigger
                        value="experience"
                        className="data-[state=active]:bg-crimson/10 data-[state=active]:text-crimson font-medium"
                      >
                        <Briefcase className="h-4 w-4 mr-2" />
                        Experience
                      </TabsTrigger>
                    </TabsList>
                  </div>

                  {/* About Tab */}
                  <TabsContent value="about" id="about" className="space-y-8">
                    {/* Education Timeline */}
                    {faculty.facultyProfile.education?.length > 0 && (
                      <section className="bg-white rounded-3xl p-8 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300">
                        <h2 className="text-2xl font-semibold mb-6 flex items-center text-gray-800">
                          <GraduationCap className="mr-3 h-6 w-6 text-crimson" />
                          Education
                        </h2>
                        <EducationTimeline education={faculty.facultyProfile.education} />
                      </section>
                    )}

                    {/* Skills */}
                    {faculty.facultyProfile.skills?.length > 0 && (
                      <section className="bg-white rounded-3xl p-8 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300">
                        <h2 className="text-2xl font-semibold mb-6 flex items-center text-gray-800">
                          <Award className="mr-3 h-6 w-6 text-crimson" />
                          Skills & Expertise
                        </h2>

                        {/* Skills Radar Chart */}
                        <div className="mb-8">
                          <SkillsRadar skills={faculty.facultyProfile.skills} />
                        </div>

                        {/* Detailed Skills Breakdown */}
                        <div className="mt-8">
                          <h3 className="text-lg font-semibold mb-4 text-gray-800">Detailed Breakdown</h3>
                          <SkillCategoryBreakdown skills={faculty.facultyProfile.skills} />
                        </div>
                      </section>
                    )}

                    {/* Academic Timeline */}
                    {faculty.facultyProfile.timeline?.length > 0 && (
                      <section className="bg-white rounded-3xl p-8 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300">
                        <h2 className="text-2xl font-semibold mb-6 flex items-center text-gray-800">
                          <Calendar className="mr-3 h-6 w-6 text-crimson" />
                          Academic Timeline
                        </h2>
                        <AcademicTimeline events={faculty.facultyProfile.timeline} />
                      </section>
                    )}
                  </TabsContent>

                  {/* Publications Tab */}
                  <TabsContent value="publications" id="publications" className="space-y-8">
                    {/* Research Impact Overview */}
                    {faculty.facultyProfile.publications?.length > 0 && (
                      <section className="bg-white rounded-3xl p-8 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300">
                        <ResearchImpact
                          publications={faculty.facultyProfile.publications}
                          hIndex={faculty.facultyProfile.hIndex}
                          citationCount={faculty.facultyProfile.citationCount}
                        />
                      </section>
                    )}

                    {/* Publication Timeline */}
                    {faculty.facultyProfile.publications?.length > 0 && (
                      <section className="bg-white rounded-3xl p-8 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300">
                        <PublicationChart publications={faculty.facultyProfile.publications} />
                      </section>
                    )}

                    {/* Detailed Publications List */}
                    <section className="bg-white rounded-3xl p-8 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300">
                      <ScholarlyPublications
                        initialPublications={faculty.facultyProfile.publications}
                        scholarId={faculty.facultyProfile.scholarId || undefined}
                      />
                    </section>
                  </TabsContent>

                  {/* Research Tab */}
                  <TabsContent value="research" id="research" className="space-y-8">
                    {/* Research Areas */}
                    {faculty.facultyProfile.researchAreas?.length > 0 && (
                      <section className="bg-white rounded-3xl p-8 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300">
                        <ResearchAreas areas={faculty.facultyProfile.researchAreas} />
                      </section>
                    )}

                    {/* Research Projects */}
                    {faculty.facultyProfile.researchProjects?.length > 0 && (
                      <section className="bg-white rounded-3xl p-8 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300">
                        <h2 className="text-2xl font-semibold mb-6 flex items-center text-gray-800">
                          <FileText className="mr-3 h-6 w-6 text-crimson" />
                          Research Projects
                        </h2>
                        <ResearchOpportunities projects={faculty.facultyProfile.researchProjects} />
                      </section>
                    )}
                  </TabsContent>

                  {/* Teaching Tab */}
                  <TabsContent value="teaching" id="teaching" className="space-y-8">
                    {/* Current Classes */}
                    <section className="bg-white rounded-3xl p-8 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300">
                      <h2 className="text-2xl font-semibold mb-6 flex items-center text-gray-800">
                        <BookOpen className="mr-3 h-6 w-6 text-crimson" />
                        Current Classes
                      </h2>
                      <UpcomingClasses classes={faculty.facultyProfile.classes} />
                    </section>

                    {/* Office Hours */}
                    {faculty.facultyProfile.officeHours && (
                      <section className="bg-white rounded-3xl p-8 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300">
                        <h2 className="text-2xl font-semibold mb-6 flex items-center text-gray-800">
                          <Clock className="mr-3 h-6 w-6 text-crimson" />
                          Office Hours
                        </h2>
                        <div className="p-6 border border-gray-200 rounded-xl bg-gradient-to-r from-gray-50 to-gray-50/50">
                          <p className="text-gray-700 text-lg">{faculty.facultyProfile.officeHours}</p>
                        </div>
                      </section>
                    )}
                  </TabsContent>

                  {/* Experience Tab */}
                  <TabsContent value="experience" id="experience" className="space-y-8">
                    {/* Industry Experience */}
                    {faculty.facultyProfile.industryExperience?.length > 0 && (
                      <section className="bg-white rounded-3xl p-8 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300">
                        <h2 className="text-2xl font-semibold mb-6 flex items-center text-gray-800">
                          <Briefcase className="mr-3 h-6 w-6 text-crimson" />
                          Industry Experience
                        </h2>
                        <div className="space-y-8">
                          {faculty.facultyProfile.industryExperience.map((exp: any) => {
                            const startDate = exp.startDate ? new Date(exp.startDate).getFullYear() : '';
                            const endDate = exp.endDate ? new Date(exp.endDate).getFullYear() : 'Present';

                            return (
                              <div key={exp.id} className="relative pl-12 border-l-2 border-gray-200 pb-8 last:pb-0">
                                <div className="absolute -left-2 top-0 h-4 w-4 rounded-full bg-crimson shadow-lg"></div>
                                <div className="bg-gray-50 rounded-xl p-6 hover:bg-gray-100 transition-colors duration-200">
                                  <div className="mb-2 text-xl font-semibold text-gray-900">{exp.position}</div>
                                  <div className="text-lg text-gray-700 mb-2 flex items-center">
                                    <Building className="h-4 w-4 mr-2 text-gray-500" />
                                    {exp.company}
                                  </div>
                                  <div className="text-sm text-gray-500 mb-4 flex items-center">
                                    <Calendar className="h-4 w-4 mr-2" />
                                    {startDate} — {endDate}
                                  </div>
                                  {exp.description && (
                                    <p className="text-gray-600 leading-relaxed">{exp.description}</p>
                                  )}
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </section>
                    )}
                  </TabsContent>
                </Tabs>
              </div>
            </div>
          </section>

          {/* Contact Section */}
          <section className="bg-gradient-to-r from-crimson/5 to-gold/5 border-t border-gray-100">
            <div className="container mx-auto px-4 max-w-6xl py-12">
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">Get in Touch</h2>
                <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                  Interested in collaboration, research opportunities, or have questions?
                  Feel free to reach out using any of the contact methods below.
                </p>
              </div>

              <div className="flex justify-center">
                <ContactCard
                  name={faculty.name || "Faculty Member"}
                  email={faculty.email}
                  phone={faculty.profile?.phone}
                  office={faculty.facultyProfile.officeLocation}
                  officeHours={faculty.facultyProfile.officeHours}
                  website={faculty.facultyProfile.websiteUrl}
                  department={faculty.facultyProfile.department.name}
                />
              </div>
            </div>
          </section>
        </main>
        <Footer />
      </div>
    </PageTransition>
  )
}
