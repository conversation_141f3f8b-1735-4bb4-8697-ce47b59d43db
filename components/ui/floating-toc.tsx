'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import {
  Menu,
  X,
  User,
  BookOpen,
  Lightbulb,
  GraduationCap,
  Briefcase,
  ChevronRight,
  Eye
} from "lucide-react"

// Icon mapping
const iconMap = {
  User,
  BookOpen,
  Lightbulb,
  GraduationCap,
  Briefcase,
  Eye
}

interface TocItem {
  id: string
  label: string
  icon: string // Changed to string
  count?: number
}

interface FloatingTocProps {
  items: TocItem[]
  activeSection?: string
}

export function FloatingToc({ items, activeSection }: FloatingTocProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      // Show TOC after scrolling past the hero section
      setIsVisible(window.scrollY > 400)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      const offset = 80 // Account for fixed header
      const elementPosition = element.offsetTop - offset
      window.scrollTo({
        top: elementPosition,
        behavior: 'smooth'
      })
    }
    setIsOpen(false)
  }

  if (!isVisible) return null

  return (
    <>
      {/* Toggle Button */}
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        className="fixed bottom-6 right-6 z-50"
      >
        <Button
          onClick={() => setIsOpen(!isOpen)}
          size="icon"
          className="h-12 w-12 rounded-full bg-crimson hover:bg-crimson/90 shadow-lg hover:shadow-xl transition-all duration-300"
        >
          {isOpen ? (
            <X className="h-5 w-5 text-white" />
          ) : (
            <Menu className="h-5 w-5 text-white" />
          )}
        </Button>
      </motion.div>

      {/* TOC Panel */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40"
              onClick={() => setIsOpen(false)}
            />

            {/* TOC Content */}
            <motion.div
              initial={{ opacity: 0, x: 300, scale: 0.9 }}
              animate={{ opacity: 1, x: 0, scale: 1 }}
              exit={{ opacity: 0, x: 300, scale: 0.9 }}
              transition={{ type: "spring", damping: 25, stiffness: 300 }}
              className="fixed bottom-20 right-6 z-50 w-80"
            >
              <Card className="shadow-2xl border-0 bg-white/95 backdrop-blur-md">
                <CardContent className="p-0">
                  <div className="p-4 border-b border-gray-100">
                    <div className="flex items-center gap-2">
                      <Eye className="h-4 w-4 text-crimson" />
                      <h3 className="font-semibold text-gray-900">Quick Navigation</h3>
                    </div>
                  </div>
                  
                  <div className="p-2 max-h-96 overflow-y-auto">
                    {items.map((item, index) => {
                      const IconComponent = iconMap[item.icon as keyof typeof iconMap] || User
                      return (
                        <motion.button
                          key={item.id}
                          initial={{ opacity: 0, x: 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.05 }}
                          onClick={() => scrollToSection(item.id)}
                          className={`
                            w-full flex items-center gap-3 p-3 rounded-lg text-left
                            transition-all duration-200 group
                            ${activeSection === item.id
                              ? 'bg-crimson/10 text-crimson border-l-2 border-crimson'
                              : 'hover:bg-gray-50 text-gray-700 hover:text-gray-900'
                            }
                          `}
                        >
                          <div className={`
                            p-1.5 rounded-md transition-colors duration-200
                            ${activeSection === item.id
                              ? 'bg-crimson/20 text-crimson'
                              : 'bg-gray-100 text-gray-500 group-hover:bg-gray-200'
                            }
                          `}>
                            <IconComponent className="h-4 w-4" />
                          </div>
                        
                        <div className="flex-1">
                          <div className="font-medium text-sm">{item.label}</div>
                          {item.count !== undefined && (
                            <div className="text-xs text-gray-500">
                              {item.count} {item.count === 1 ? 'item' : 'items'}
                            </div>
                          )}
                        </div>
                        
                        <ChevronRight className={`
                          h-4 w-4 transition-all duration-200
                          ${activeSection === item.id 
                            ? 'text-crimson translate-x-1' 
                            : 'text-gray-400 group-hover:text-gray-600 group-hover:translate-x-1'
                          }
                        `} />
                        </motion.button>
                      )
                    })}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  )
}

// Hook to track active section
export function useActiveSection(sectionIds: string[]) {
  const [activeSection, setActiveSection] = useState<string>('')

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setActiveSection(entry.target.id)
          }
        })
      },
      {
        rootMargin: '-20% 0px -70% 0px',
        threshold: 0.1
      }
    )

    sectionIds.forEach((id) => {
      const element = document.getElementById(id)
      if (element) {
        observer.observe(element)
      }
    })

    return () => {
      sectionIds.forEach((id) => {
        const element = document.getElementById(id)
        if (element) {
          observer.unobserve(element)
        }
      })
    }
  }, [sectionIds])

  return activeSection
}
