'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON>, TrendingUp } from "lucide-react"

interface Publication {
  id: string
  title: string
  year: number
  journal: string
  citationCount: number
}

interface PublicationChartProps {
  publications: Publication[]
}

export function PublicationChart({ publications }: PublicationChartProps) {
  // Group publications by year
  const publicationsByYear = publications.reduce((acc, pub) => {
    acc[pub.year] = (acc[pub.year] || 0) + 1
    return acc
  }, {} as Record<number, number>)

  // Get years range
  const years = Object.keys(publicationsByYear).map(Number).sort()
  const minYear = Math.min(...years)
  const maxYear = Math.max(...years)
  
  // Fill in missing years with 0
  const completeData = []
  for (let year = minYear; year <= maxYear; year++) {
    completeData.push({
      year,
      count: publicationsByYear[year] || 0
    })
  }

  const maxCount = Math.max(...completeData.map(d => d.count))

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart className="h-5 w-5 text-crimson" />
          Publications Over Time
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Chart */}
          <div className="relative h-64 flex items-end justify-between gap-2 p-4 bg-gray-50 rounded-lg">
            {completeData.map((data, index) => (
              <motion.div
                key={data.year}
                initial={{ height: 0 }}
                animate={{ height: `${(data.count / maxCount) * 100}%` }}
                transition={{ delay: index * 0.1, duration: 0.8 }}
                className="flex flex-col items-center min-w-0 flex-1"
              >
                <div 
                  className="w-full bg-gradient-to-t from-crimson to-gold rounded-t-sm min-h-[4px] relative group cursor-pointer"
                  style={{ height: data.count > 0 ? `${(data.count / maxCount) * 100}%` : '4px' }}
                >
                  {/* Tooltip */}
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                    {data.year}: {data.count} publication{data.count !== 1 ? 's' : ''}
                  </div>
                </div>
                <div className="text-xs text-gray-600 mt-1 transform -rotate-45 origin-left">
                  {data.year}
                </div>
              </motion.div>
            ))}
          </div>

          {/* Summary Stats */}
          <div className="grid grid-cols-3 gap-4 pt-4 border-t border-gray-100">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{publications.length}</div>
              <div className="text-sm text-gray-600">Total Publications</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {publications.reduce((sum, pub) => sum + pub.citationCount, 0)}
              </div>
              <div className="text-sm text-gray-600">Total Citations</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {completeData.length > 0 ? (publications.length / completeData.length).toFixed(1) : '0'}
              </div>
              <div className="text-sm text-gray-600">Avg per Year</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Research impact visualization
interface ResearchImpactProps {
  publications: Publication[]
  hIndex?: number | null
  citationCount?: number | null
}

export function ResearchImpact({ publications, hIndex, citationCount }: ResearchImpactProps) {
  // Calculate impact metrics
  const totalCitations = citationCount || publications.reduce((sum, pub) => sum + pub.citationCount, 0)
  const avgCitationsPerPaper = publications.length > 0 ? (totalCitations / publications.length).toFixed(1) : '0'
  
  // Find most cited papers
  const topPapers = [...publications]
    .sort((a, b) => b.citationCount - a.citationCount)
    .slice(0, 5)

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Impact Metrics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-crimson" />
            Research Impact
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <span className="text-gray-700">H-Index</span>
              <span className="text-2xl font-bold text-crimson">{hIndex || 'N/A'}</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <span className="text-gray-700">Total Citations</span>
              <span className="text-2xl font-bold text-gold">{totalCitations.toLocaleString()}</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <span className="text-gray-700">Avg Citations/Paper</span>
              <span className="text-2xl font-bold text-blue-600">{avgCitationsPerPaper}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Top Papers */}
      <Card>
        <CardHeader>
          <CardTitle>Most Cited Papers</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {topPapers.map((paper, index) => (
              <motion.div
                key={paper.id}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <div className="flex-shrink-0 w-6 h-6 bg-crimson text-white rounded-full flex items-center justify-center text-sm font-bold">
                  {index + 1}
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-gray-900 line-clamp-2 text-sm">
                    {paper.title}
                  </h4>
                  <div className="flex items-center gap-2 mt-1">
                    <span className="text-xs text-gray-500">{paper.year}</span>
                    <span className="text-xs text-gray-400">•</span>
                    <span className="text-xs font-medium text-crimson">
                      {paper.citationCount} citations
                    </span>
                  </div>
                </div>
              </motion.div>
            ))}
            {topPapers.length === 0 && (
              <div className="text-center text-gray-500 py-4">
                No publications available
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
