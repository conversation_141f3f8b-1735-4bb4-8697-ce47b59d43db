'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  TrendingUp, 
  BookOpen, 
  Award, 
  Users, 
  Star,
  BarChart,
  Calendar,
  Target
} from "lucide-react"

interface FacultyStatsProps {
  publications: number
  citationCount?: number | null
  hIndex?: number | null
  yearsExperience: number
  researchAreas: number
  currentClasses: number
}

export function FacultyStatsCards({ 
  publications, 
  citationCount, 
  hIndex, 
  yearsExperience,
  researchAreas,
  currentClasses 
}: FacultyStatsProps) {
  const stats = [
    {
      icon: BookOpen,
      label: "Publications",
      value: publications,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      borderColor: "border-blue-200"
    },
    {
      icon: TrendingUp,
      label: "Citations",
      value: citationCount || 0,
      color: "text-green-600",
      bgColor: "bg-green-50",
      borderColor: "border-green-200"
    },
    {
      icon: Star,
      label: "H-Index",
      value: hIndex || 0,
      color: "text-yellow-600",
      bgColor: "bg-yellow-50",
      borderColor: "border-yellow-200"
    },
    {
      icon: Calendar,
      label: "Years Experience",
      value: yearsExperience,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      borderColor: "border-purple-200"
    },
    {
      icon: Target,
      label: "Research Areas",
      value: researchAreas,
      color: "text-crimson",
      bgColor: "bg-crimson/5",
      borderColor: "border-crimson/20"
    },
    {
      icon: Users,
      label: "Current Classes",
      value: currentClasses,
      color: "text-gold",
      bgColor: "bg-gold/5",
      borderColor: "border-gold/20"
    }
  ]

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-8">
      {stats.map((stat, index) => (
        <motion.div
          key={stat.label}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
        >
          <Card className={`border ${stat.borderColor} hover:shadow-md transition-all duration-300 group cursor-pointer`}>
            <CardContent className="p-4 text-center">
              <div className={`w-12 h-12 ${stat.bgColor} rounded-full flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300`}>
                <stat.icon className={`h-6 w-6 ${stat.color}`} />
              </div>
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {stat.value.toLocaleString()}
              </div>
              <div className="text-xs text-gray-600 font-medium">
                {stat.label}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      ))}
    </div>
  )
}

// Quick action buttons component
interface QuickActionsProps {
  email: string
  websiteUrl?: string | null
  scholarId?: string | null
  phone?: string | null
}

export function QuickActionButtons({ email, websiteUrl, scholarId, phone }: QuickActionsProps) {
  const actions = [
    {
      icon: Mail,
      label: "Email",
      href: `mailto:${email}`,
      color: "text-crimson",
      bgColor: "bg-crimson/5",
      hoverColor: "hover:bg-crimson/10"
    },
    ...(phone ? [{
      icon: Phone,
      label: "Call",
      href: `tel:${phone}`,
      color: "text-green-600",
      bgColor: "bg-green-50",
      hoverColor: "hover:bg-green-100"
    }] : []),
    ...(websiteUrl ? [{
      icon: Globe,
      label: "Website",
      href: websiteUrl,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      hoverColor: "hover:bg-blue-100",
      external: true
    }] : []),
    ...(scholarId ? [{
      icon: Award,
      label: "Scholar",
      href: `https://scholar.google.com/citations?user=${scholarId}`,
      color: "text-gold",
      bgColor: "bg-gold/5",
      hoverColor: "hover:bg-gold/10",
      external: true
    }] : [])
  ]

  return (
    <div className="flex flex-wrap gap-3 mb-6">
      {actions.map((action) => (
        <a
          key={action.label}
          href={action.href}
          target={action.external ? "_blank" : undefined}
          rel={action.external ? "noopener noreferrer" : undefined}
          className={`
            flex items-center gap-2 px-4 py-2.5 rounded-xl border border-gray-200
            ${action.bgColor} ${action.hoverColor} ${action.color}
            transition-all duration-300 hover:shadow-md hover:scale-105
            font-medium text-sm group
          `}
        >
          <action.icon className="h-4 w-4 group-hover:scale-110 transition-transform duration-300" />
          <span>{action.label}</span>
          {action.external && (
            <ArrowUpRight className="h-3 w-3 opacity-0 -translate-x-1 group-hover:opacity-100 group-hover:translate-x-0 transition-all duration-300" />
          )}
        </a>
      ))}
    </div>
  )
}

// Achievement badges component
interface AchievementBadgesProps {
  publications: number
  citationCount?: number | null
  yearsExperience: number
}

export function AchievementBadges({ publications, citationCount, yearsExperience }: AchievementBadgesProps) {
  const achievements = []

  if (publications >= 50) {
    achievements.push({ label: "Prolific Researcher", icon: BookOpen, color: "bg-blue-100 text-blue-800" })
  } else if (publications >= 20) {
    achievements.push({ label: "Active Publisher", icon: BookOpen, color: "bg-blue-50 text-blue-700" })
  }

  if (citationCount && citationCount >= 1000) {
    achievements.push({ label: "Highly Cited", icon: TrendingUp, color: "bg-green-100 text-green-800" })
  } else if (citationCount && citationCount >= 500) {
    achievements.push({ label: "Well Cited", icon: TrendingUp, color: "bg-green-50 text-green-700" })
  }

  if (yearsExperience >= 20) {
    achievements.push({ label: "Veteran Educator", icon: Award, color: "bg-purple-100 text-purple-800" })
  } else if (yearsExperience >= 10) {
    achievements.push({ label: "Experienced Faculty", icon: Award, color: "bg-purple-50 text-purple-700" })
  }

  if (achievements.length === 0) return null

  return (
    <div className="flex flex-wrap gap-2 mb-6">
      {achievements.map((achievement, index) => (
        <motion.div
          key={achievement.label}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: index * 0.1 }}
        >
          <Badge 
            variant="secondary" 
            className={`${achievement.color} border-0 px-3 py-1.5 text-xs font-medium flex items-center gap-1.5`}
          >
            <achievement.icon className="h-3 w-3" />
            {achievement.label}
          </Badge>
        </motion.div>
      ))}
    </div>
  )
}
