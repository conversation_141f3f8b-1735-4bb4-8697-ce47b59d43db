'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Mail, 
  Phone, 
  MapPin, 
  Clock, 
  Globe, 
  MessageCircle,
  Calendar,
  Copy,
  Check,
  ExternalLink
} from "lucide-react"

interface ContactCardProps {
  name: string
  email: string
  phone?: string | null
  office?: string | null
  officeHours?: string | null
  website?: string | null
  department: string
}

export function ContactCard({ 
  name, 
  email, 
  phone, 
  office, 
  officeHours, 
  website, 
  department 
}: ContactCardProps) {
  const [copiedField, setCopiedField] = useState<string | null>(null)

  const copyToClipboard = async (text: string, field: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedField(field)
      setTimeout(() => setCopiedField(null), 2000)
    } catch (err) {
      console.error('Failed to copy text: ', err)
    }
  }

  const contactMethods = [
    {
      icon: Mail,
      label: 'Email',
      value: email,
      href: `mailto:${email}`,
      copyable: true,
      color: 'text-crimson'
    },
    ...(phone ? [{
      icon: Phone,
      label: 'Phone',
      value: phone,
      href: `tel:${phone}`,
      copyable: true,
      color: 'text-green-600'
    }] : []),
    ...(office ? [{
      icon: MapPin,
      label: 'Office',
      value: office,
      href: null,
      copyable: false,
      color: 'text-blue-600'
    }] : []),
    ...(officeHours ? [{
      icon: Clock,
      label: 'Office Hours',
      value: officeHours,
      href: null,
      copyable: false,
      color: 'text-purple-600'
    }] : []),
    ...(website ? [{
      icon: Globe,
      label: 'Website',
      value: website,
      href: website,
      copyable: true,
      color: 'text-indigo-600',
      external: true
    }] : [])
  ]

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center pb-4">
        <CardTitle className="text-xl">Contact Information</CardTitle>
        <div className="space-y-1">
          <p className="font-medium text-gray-900">{name}</p>
          <Badge variant="outline" className="text-xs">
            {department}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {contactMethods.map((method, index) => (
          <motion.div
            key={method.label}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
            className="flex items-center gap-3 p-3 rounded-lg border border-gray-100 hover:border-gray-200 transition-colors group"
          >
            <div className={`p-2 rounded-md bg-gray-50 group-hover:bg-gray-100 transition-colors ${method.color}`}>
              <method.icon className="h-4 w-4" />
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="text-sm font-medium text-gray-700">{method.label}</div>
              <div className="text-sm text-gray-900 truncate">{method.value}</div>
            </div>
            
            <div className="flex items-center gap-1">
              {method.copyable && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => copyToClipboard(method.value, method.label)}
                >
                  {copiedField === method.label ? (
                    <Check className="h-3 w-3 text-green-600" />
                  ) : (
                    <Copy className="h-3 w-3" />
                  )}
                </Button>
              )}
              
              {method.href && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  asChild
                >
                  <a 
                    href={method.href}
                    target={method.external ? "_blank" : undefined}
                    rel={method.external ? "noopener noreferrer" : undefined}
                  >
                    {method.external ? (
                      <ExternalLink className="h-3 w-3" />
                    ) : (
                      <method.icon className="h-3 w-3" />
                    )}
                  </a>
                </Button>
              )}
            </div>
          </motion.div>
        ))}
        
        {/* Quick Actions */}
        <div className="pt-4 border-t border-gray-100 space-y-3">
          <Button 
            className="w-full bg-crimson hover:bg-crimson/90"
            asChild
          >
            <a href={`mailto:${email}`}>
              <Mail className="h-4 w-4 mr-2" />
              Send Email
            </a>
          </Button>
          
          <div className="grid grid-cols-2 gap-2">
            <Button 
              variant="outline" 
              size="sm"
              asChild
            >
              <a href={`mailto:${email}?subject=Meeting Request`}>
                <Calendar className="h-4 w-4 mr-1" />
                Schedule Meeting
              </a>
            </Button>
            
            <Button 
              variant="outline" 
              size="sm"
              asChild
            >
              <a href={`mailto:${email}?subject=Quick Question`}>
                <MessageCircle className="h-4 w-4 mr-1" />
                Ask Question
              </a>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Compact contact info for sidebar
interface CompactContactProps {
  email: string
  phone?: string | null
  office?: string | null
  officeHours?: string | null
}

export function CompactContact({ email, phone, office, officeHours }: CompactContactProps) {
  return (
    <div className="space-y-3">
      <div className="flex items-center gap-2 text-sm">
        <Mail className="h-4 w-4 text-crimson" />
        <a 
          href={`mailto:${email}`}
          className="text-gray-700 hover:text-crimson transition-colors truncate"
        >
          {email}
        </a>
      </div>
      
      {phone && (
        <div className="flex items-center gap-2 text-sm">
          <Phone className="h-4 w-4 text-green-600" />
          <a 
            href={`tel:${phone}`}
            className="text-gray-700 hover:text-green-600 transition-colors"
          >
            {phone}
          </a>
        </div>
      )}
      
      {office && (
        <div className="flex items-center gap-2 text-sm">
          <MapPin className="h-4 w-4 text-blue-600" />
          <span className="text-gray-700">{office}</span>
        </div>
      )}
      
      {officeHours && (
        <div className="flex items-center gap-2 text-sm">
          <Clock className="h-4 w-4 text-purple-600" />
          <span className="text-gray-700">{officeHours}</span>
        </div>
      )}
    </div>
  )
}
