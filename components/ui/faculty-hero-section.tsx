'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { LazyImage } from "@/components/ui/lazy-image"
import { 
  MapPin, 
  Clock, 
  Building, 
  Quote,
  Hash,
  ChevronRight,
  Sparkles
} from "lucide-react"

interface FacultyHeroProps {
  name: string
  title?: string
  bio?: string
  department: {
    name: string
  }
  officeLocation?: string | null
  officeHours?: string | null
  imageUrl: string
  researchAreas: Array<{ id: string; areaName: string }>
}

export function FacultyHeroSection({
  name,
  title,
  bio,
  department,
  officeLocation,
  officeHours,
  imageUrl,
  researchAreas
}: FacultyHeroProps) {
  return (
    <div className="relative overflow-hidden">
      {/* Enhanced background with animated elements */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-crimson/3 via-transparent to-gold/3"></div>
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-b from-white/80 via-white/60 to-white/80"></div>
        
        {/* Animated background elements */}
        <motion.div
          animate={{ 
            x: [0, 100, 0],
            y: [0, -50, 0],
            rotate: [0, 180, 360]
          }}
          transition={{ 
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute -top-24 -left-24 w-96 h-96 rounded-full bg-crimson/5 blur-3xl"
        />
        <motion.div
          animate={{ 
            x: [0, -80, 0],
            y: [0, 60, 0],
            rotate: [360, 180, 0]
          }}
          transition={{ 
            duration: 25,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-20 right-10 w-80 h-80 rounded-full bg-gold/5 blur-3xl"
        />
        
        {/* Floating particles */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-crimson/20 rounded-full"
            style={{
              left: `${20 + i * 15}%`,
              top: `${30 + (i % 3) * 20}%`,
            }}
            animate={{
              y: [0, -20, 0],
              opacity: [0.3, 0.8, 0.3],
            }}
            transition={{
              duration: 3 + i * 0.5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: i * 0.2,
            }}
          />
        ))}
      </div>

      <div className="container mx-auto px-4 max-w-6xl pt-8 pb-12 relative z-10">
        <div className="flex flex-col lg:flex-row gap-12 items-start">
          {/* Enhanced Profile Image Section */}
          <motion.div 
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="lg:w-1/3 xl:w-1/4 max-w-[320px] mx-auto lg:mx-0"
          >
            <div className="relative group">
              {/* Decorative frame */}
              <div className="absolute -inset-4 bg-gradient-to-r from-crimson/20 to-gold/20 rounded-3xl blur-lg opacity-75 group-hover:opacity-100 transition-opacity duration-500"></div>
              
              <div className="relative overflow-hidden rounded-3xl border-4 border-white shadow-2xl bg-white">
                <LazyImage
                  src={imageUrl}
                  alt={name}
                  aspectRatio="aspect-[3/4]"
                  className="h-full w-full object-cover transition-transform duration-700 group-hover:scale-105"
                />
                
                {/* Overlay gradient on hover */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                
                {/* Floating badge */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                  className="absolute top-4 right-4"
                >
                  <Badge className="bg-white/90 text-crimson border-0 shadow-lg backdrop-blur-sm">
                    <Sparkles className="h-3 w-3 mr-1" />
                    Faculty
                  </Badge>
                </motion.div>
              </div>
            </div>

            {/* Contact Info Card */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="mt-6"
            >
              <Card className="bg-white/80 backdrop-blur-sm border border-gray-100 shadow-lg">
                <CardContent className="p-5">
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-sm font-medium text-gray-500 mb-2 flex items-center">
                        <Building className="h-4 w-4 mr-1.5" />
                        Department
                      </h3>
                      <div className="text-base font-medium text-crimson">
                        {department.name}
                      </div>
                    </div>
                    
                    {title && (
                      <div className="pt-3 border-t border-gray-100">
                        <h3 className="text-sm font-medium text-gray-500 mb-1">Title</h3>
                        <p className="text-base text-gray-900">{title}</p>
                      </div>
                    )}
                    
                    {officeLocation && (
                      <div className="pt-3 border-t border-gray-100">
                        <h3 className="text-sm font-medium text-gray-500 mb-1">Office</h3>
                        <p className="text-base text-gray-900 flex items-center gap-1">
                          <MapPin className="h-4 w-4 text-gray-400" />
                          <span>{officeLocation}</span>
                        </p>
                      </div>
                    )}
                    
                    {officeHours && (
                      <div className="pt-3 border-t border-gray-100">
                        <h3 className="text-sm font-medium text-gray-500 mb-1">Office Hours</h3>
                        <p className="text-base text-gray-900 flex items-center gap-1">
                          <Clock className="h-4 w-4 text-gray-400" />
                          <span>{officeHours}</span>
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>

          {/* Enhanced Content Section */}
          <motion.div 
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="lg:w-2/3 xl:w-3/4 space-y-8"
          >
            {/* Name and Title */}
            <div>
              <motion.h1 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="text-5xl font-bold text-gray-900 mb-4 bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text"
              >
                {name}
              </motion.h1>
              
              {title && (
                <motion.p 
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                  className="text-xl text-gray-600 mb-6 font-medium"
                >
                  {title}
                </motion.p>
              )}
            </div>

            {/* Bio Section */}
            {bio && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
                className="relative"
              >
                <Quote className="absolute -top-4 -left-4 h-8 w-8 text-crimson/20" />
                <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-gray-100 shadow-sm">
                  <p className="text-lg text-gray-700 leading-relaxed relative z-10">
                    {bio}
                  </p>
                </div>
              </motion.div>
            )}

            {/* Research Areas */}
            {researchAreas.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7 }}
              >
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <Hash className="h-5 w-5 mr-2 text-crimson" />
                  Research Interests
                </h3>
                <div className="flex flex-wrap gap-3">
                  {researchAreas.map((area, index) => (
                    <motion.div
                      key={area.id}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: 0.8 + index * 0.1 }}
                    >
                      <Badge 
                        variant="secondary"
                        className="px-4 py-2 text-sm font-medium bg-gradient-to-r from-crimson/10 to-gold/10 text-crimson border border-crimson/20 hover:from-crimson/20 hover:to-gold/20 transition-all duration-300 cursor-pointer"
                      >
                        {area.areaName}
                      </Badge>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  )
}
