'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Target, Zap } from "lucide-react"

interface Skill {
  id: string
  skillName: string
  proficiency: string
  category: string
}

interface SkillsRadarProps {
  skills: Skill[]
}

export function SkillsRadar({ skills }: SkillsRadarProps) {
  // Group skills by category and calculate average proficiency
  const skillsByCategory = skills.reduce((acc, skill) => {
    const proficiency = parseInt(skill.proficiency) || 0
    if (!acc[skill.category]) {
      acc[skill.category] = {
        skills: [],
        totalProficiency: 0,
        count: 0
      }
    }
    acc[skill.category].skills.push(skill)
    acc[skill.category].totalProficiency += proficiency
    acc[skill.category].count += 1
    return acc
  }, {} as Record<string, { skills: Skill[], totalProficiency: number, count: number }>)

  // Calculate average proficiency for each category
  const categoryData = Object.entries(skillsByCategory).map(([category, data]) => ({
    category,
    avgProficiency: data.totalProficiency / data.count,
    skills: data.skills,
    count: data.count
  })).sort((a, b) => b.avgProficiency - a.avgProficiency)

  // Color scheme for categories
  const colors = [
    'from-crimson to-red-400',
    'from-gold to-yellow-400', 
    'from-blue-500 to-blue-400',
    'from-green-500 to-green-400',
    'from-purple-500 to-purple-400',
    'from-indigo-500 to-indigo-400'
  ]

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Skills Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5 text-crimson" />
            Skills Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {categoryData.map((category, index) => (
              <motion.div
                key={category.category}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="space-y-2"
              >
                <div className="flex justify-between items-center">
                  <h3 className="font-medium text-gray-900">{category.category}</h3>
                  <Badge variant="outline" className="text-xs">
                    {category.count} skill{category.count !== 1 ? 's' : ''}
                  </Badge>
                </div>
                
                {/* Progress bar */}
                <div className="relative h-3 bg-gray-100 rounded-full overflow-hidden">
                  <motion.div
                    initial={{ width: 0 }}
                    animate={{ width: `${category.avgProficiency}%` }}
                    transition={{ delay: index * 0.1 + 0.3, duration: 1 }}
                    className={`h-full bg-gradient-to-r ${colors[index % colors.length]} rounded-full`}
                  />
                  <div className="absolute inset-0 flex items-center justify-end pr-2">
                    <span className="text-xs font-medium text-white drop-shadow">
                      {Math.round(category.avgProficiency)}%
                    </span>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Top Skills */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-gold" />
            Top Skills
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {skills
              .sort((a, b) => parseInt(b.proficiency) - parseInt(a.proficiency))
              .slice(0, 8)
              .map((skill, index) => (
                <motion.div
                  key={skill.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <div>
                    <div className="font-medium text-gray-900">{skill.skillName}</div>
                    <div className="text-sm text-gray-500">{skill.category}</div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-gradient-to-r from-crimson to-gold rounded-full transition-all duration-1000"
                        style={{ width: `${skill.proficiency}%` }}
                      />
                    </div>
                    <span className="text-sm font-medium text-gray-700 w-8 text-right">
                      {skill.proficiency}%
                    </span>
                  </div>
                </motion.div>
              ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// Skill category breakdown component
interface SkillCategoryBreakdownProps {
  skills: Skill[]
}

export function SkillCategoryBreakdown({ skills }: SkillCategoryBreakdownProps) {
  const categories = [...new Set(skills.map(skill => skill.category))]
  
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {categories.map((category, index) => {
        const categorySkills = skills.filter(skill => skill.category === category)
        const avgProficiency = categorySkills.reduce((sum, skill) => sum + parseInt(skill.proficiency), 0) / categorySkills.length
        
        return (
          <motion.div
            key={category}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="h-full hover:shadow-md transition-shadow duration-300">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">{category}</CardTitle>
                <div className="text-sm text-gray-500">
                  {categorySkills.length} skill{categorySkills.length !== 1 ? 's' : ''}
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {/* Average proficiency indicator */}
                  <div className="flex items-center gap-2 mb-4">
                    <div className="flex-1 h-2 bg-gray-100 rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-gradient-to-r from-crimson to-gold rounded-full"
                        style={{ width: `${avgProficiency}%` }}
                      />
                    </div>
                    <span className="text-sm font-medium text-gray-700">
                      {Math.round(avgProficiency)}%
                    </span>
                  </div>
                  
                  {/* Individual skills */}
                  <div className="space-y-2">
                    {categorySkills.map(skill => (
                      <div key={skill.id} className="flex justify-between items-center text-sm">
                        <span className="text-gray-700">{skill.skillName}</span>
                        <span className="text-gray-500">{skill.proficiency}%</span>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )
      })}
    </div>
  )
}
